package main

import (
	"context"
	"errors"
	"log"
	"os"
	"os/signal"
	"sync"
	"syscall"
	"time"

	"github.com/getsentry/sentry-go"

	"uniscribe-service/internal/config"
	"uniscribe-service/internal/handlers"
)

func main() {
	// 初始化任务队列
	log.Printf("uniscribe-service started with PID: %d\n", os.Getpid())
	config.LoadConfig()

	err := sentry.Init(sentry.ClientOptions{
		Dsn:              "https://<EMAIL>/4508829186064384",
		TracesSampleRate: 1.0,
	})
	if err != nil {
		log.Fatalf("sentry.Init: %s", err)
	}

	MAX_AUDIO_TASK_COUNT := 10
	MAX_TEXT_TASK_COUNT := 10

	resultSender := handlers.NewHTTPResultSender(config.Cfg.WebBackendHost)
	audioTaskProcessor := handlers.NewAudioTaskProcessor(resultSender)
	textTaskProcessor := handlers.NewTextTaskProcessor(resultSender)

	audioTaskQueue := make(chan handlers.Task, MAX_AUDIO_TASK_COUNT)
	textTaskQueue := make(chan handlers.Task, MAX_TEXT_TASK_COUNT)

	// 创建一个 context，用于控制 goroutine 的生命周期
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// 创建一个 WaitGroup 来等待所有 goroutine 完成
	var wg sync.WaitGroup

	// 启动多个音频处理任务的 goroutine
	for i := range make([]struct{}, MAX_AUDIO_TASK_COUNT) {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()
			log.Printf("Starting audio processor #%d", id)
			processAudioTasks(ctx, audioTaskProcessor, audioTaskQueue)
		}(i)
	}

	// 启动多个文本处理任务的 goroutine
	for i := range make([]struct{}, MAX_TEXT_TASK_COUNT) {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()
			log.Printf("Starting text processor #%d", id)
			processTextTasks(ctx, textTaskProcessor, textTaskQueue)
		}(i)
	}

	// 设置优雅退出
	shutdown := make(chan os.Signal, 1)
	signal.Notify(shutdown, os.Interrupt, syscall.SIGTERM)

	// 启动音频任务获取器
	go func() {
		for {
			select {
			case <-ctx.Done():
				return
			default:
				url := config.Cfg.WebBackendHost + "/tasks/next?type=transcription"
				task, err := handlers.FetchNextTask(url)
				if err != nil {
					if errors.Is(err, handlers.ErrorNoTask{}) {
						time.Sleep(5 * time.Second)
					} else {
						time.Sleep(1 * time.Second)
						log.Printf("failed to fetch transcription task: %v", err)
					}
					continue
				}
				log.Printf("fetched transcription task: %v", task)
				audioTaskQueue <- task
			}
		}
	}()

	// 启动文本任务获取器
	go func() {
		for {
			select {
			case <-ctx.Done():
				return
			default:
				url := config.Cfg.WebBackendHost + "/tasks/next?type=text"
				task, err := handlers.FetchNextTask(url)
				if err != nil {
					if errors.Is(err, handlers.ErrorNoTask{}) {
						time.Sleep(5 * time.Second)
					} else {
						time.Sleep(1 * time.Second)
						log.Printf("failed to fetch text task: %v", err)
					}
					continue
				}
				log.Printf("fetched text task: %v", task)
				textTaskQueue <- task
			}
		}
	}()

	// 等待关闭信号
	<-shutdown
	log.Println("uniscribe-service shutdown signal received")
	// 取消 context， 让所有 goroutine 退出
	cancel()

	// 关闭任务队列
	close(audioTaskQueue)
	close(textTaskQueue)

	// sentry flush
	sentry.Flush(2 * time.Second)

	wg.Wait()
	log.Println("uniscribe-service shutdown complete")
}

func processAudioTasks(ctx context.Context, processor handlers.AudioTaskProcessor, taskQueue <-chan handlers.Task) {
	for {
		select {
		case <-ctx.Done():
			log.Println("[INFO] processAudioTasks context done, exiting")
			return
		case task, ok := <-taskQueue:
			if !ok {
				log.Println("[INFO] Audio task queue closed, exiting")
				return
			}

			startTime := time.Now()

			if err := processor.Process(ctx, task); err != nil {
				sentry.CaptureException(err)
				log.Printf("[ERROR] Failed to process audio task %d: %v", task.TaskID, err)
			} else {
				duration := time.Since(startTime)
				log.Printf("[INFO] Successfully processed audio task %d in %v", task.TaskID, duration)
			}
		default:
			// 避免CPU空转，使用较大的休眠时间（500ms）
			// 保留此改动：转录任务通常需要较长时间处理，较大的休眠时间可以显著降低CPU使用率
			time.Sleep(500 * time.Millisecond)
		}
	}
}

func processTextTasks(ctx context.Context, processor handlers.TextTaskProcessor, taskQueue <-chan handlers.Task) {
	for {
		select {
		case <-ctx.Done():
			log.Println("[INFO] processTextTasks context done, exiting")
			return
		case task, ok := <-taskQueue:
			if !ok {
				log.Println("[INFO] Text task queue closed, exiting")
				return
			}

			startTime := time.Now()

			if err := processor.Process(ctx, task); err != nil {
				sentry.CaptureException(err)
				log.Printf("[ERROR] Failed to process text task %d: %v", task.TaskID, err)
			} else {
				duration := time.Since(startTime)
				log.Printf("[INFO] Successfully processed text task %d in %v", task.TaskID, duration)
			}
		default:
			// 避免CPU空转，使用较大的休眠时间（500ms）
			// 保留此改动：文本处理任务通常需要较长时间处理，较大的休眠时间可以显著降低CPU使用率
			time.Sleep(500 * time.Millisecond)
		}
	}
}
